from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from models.database import get_db
from models.partner import Partner
from models.user import User
from schemas.partner import Partner as PartnerSchema, PartnerCreate, PartnerUpdate, PartnerWithBalance
from services.balance_calculator import BalanceCalculator
from utils.dependencies import get_current_user

router = APIRouter()

@router.get("/", response_model=List[PartnerWithBalance])
async def get_partners(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    partners = db.query(Partner).offset(skip).limit(limit).all()
    
    partners_with_balance = []
    for partner in partners:
        balance_info = BalanceCalculator.calculate_partner_balance(db, partner.id)
        partner_dict = partner.__dict__.copy()
        partner_dict.update(balance_info)
        partners_with_balance.append(partner_dict)
    
    return partners_with_balance

@router.post("/", response_model=PartnerSchema)
async def create_partner(
    partner: Partner<PERSON><PERSON>, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    db_partner = Partner(**partner.dict())
    db.add(db_partner)
    db.commit()
    db.refresh(db_partner)
    return db_partner

@router.get("/{partner_id}", response_model=PartnerWithBalance)
async def get_partner(
    partner_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    partner = db.query(Partner).filter(Partner.id == partner_id).first()
    if not partner:
        raise HTTPException(status_code=404, detail="Partner not found")
    
    balance_info = BalanceCalculator.calculate_partner_balance(db, partner_id)
    partner_dict = partner.__dict__.copy()
    partner_dict.update(balance_info)
    
    return partner_dict

@router.put("/{partner_id}", response_model=PartnerSchema)
async def update_partner(
    partner_id: int, 
    partner_update: PartnerUpdate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    partner = db.query(Partner).filter(Partner.id == partner_id).first()
    if not partner:
        raise HTTPException(status_code=404, detail="Partner not found")
    
    update_data = partner_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(partner, field, value)
    
    db.commit()
    db.refresh(partner)
    return partner

@router.get("/{partner_id}/balance")
async def get_partner_balance(
    partner_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    partner = db.query(Partner).filter(Partner.id == partner_id).first()
    if not partner:
        raise HTTPException(status_code=404, detail="Partner not found")
    
    return BalanceCalculator.calculate_partner_balance(db, partner_id)
