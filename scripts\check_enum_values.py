#!/usr/bin/env python3
"""
Check enum values in the database vs model expectations
"""

import sqlite3
import os

def check_enum_values():
    """Check enum values in the database"""
    print("🔍 Checking enum values in database...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'bureau_de_change.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check transaction_type values
        print("\n📊 Transaction Type values:")
        cursor.execute("SELECT DISTINCT transaction_type FROM transactions")
        types = cursor.fetchall()
        for t in types:
            print(f"   - '{t[0]}'")
        
        # Check status values
        print("\n📊 Status values:")
        cursor.execute("SELECT DISTINCT status FROM transactions")
        statuses = cursor.fetchall()
        for s in statuses:
            print(f"   - '{s[0]}'")
        
        # Check channel values
        print("\n📊 Channel values:")
        cursor.execute("SELECT DISTINCT channel FROM transactions")
        channels = cursor.fetchall()
        for c in channels:
            print(f"   - '{c[0]}'")
        
        # Show expected enum values
        print("\n🎯 Expected enum values:")
        print("   Transaction Types: 'funds_given', 'funds_returned', 'partial_return'")
        print("   Statuses: 'pending', 'completed', 'cancelled'")
        print("   Channels: 'cash', 'bank_transfer'")
        
        # Check for mismatches
        print("\n⚠️  Potential issues:")
        
        # Check transaction types
        cursor.execute("SELECT DISTINCT transaction_type FROM transactions")
        db_types = [t[0] for t in cursor.fetchall()]
        expected_types = ['funds_given', 'funds_returned', 'partial_return']
        
        for db_type in db_types:
            if db_type.upper() in ['FUNDS_GIVEN', 'FUNDS_RETURNED', 'PARTIAL_RETURN']:
                print(f"   ❌ Transaction type '{db_type}' is UPPERCASE, expected lowercase")
        
        # Check statuses
        cursor.execute("SELECT DISTINCT status FROM transactions")
        db_statuses = [s[0] for s in cursor.fetchall()]
        
        for db_status in db_statuses:
            if db_status.upper() in ['PENDING', 'COMPLETED', 'CANCELLED']:
                print(f"   ❌ Status '{db_status}' is UPPERCASE, expected lowercase")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_enum_values()
