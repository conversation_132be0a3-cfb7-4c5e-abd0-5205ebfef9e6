#!/usr/bin/env python3
"""
Check enum values in the database vs model expectations
"""

import sqlite3
import os

def check_enum_values():
    """Check enum values in the database"""
    print("🔍 Checking enum values in database...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'bureau_de_change.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check transaction_type values
        print("\n📊 Transaction Type values:")
        cursor.execute("SELECT DISTINCT transaction_type FROM transactions")
        types = cursor.fetchall()
        for t in types:
            print(f"   - '{t[0]}'")
        
        # Check status values
        print("\n📊 Status values:")
        cursor.execute("SELECT DISTINCT status FROM transactions")
        statuses = cursor.fetchall()
        for s in statuses:
            print(f"   - '{s[0]}'")
        
        # Check channel values
        print("\n📊 Channel values:")
        cursor.execute("SELECT DISTINCT channel FROM transactions")
        channels = cursor.fetchall()
        for c in channels:
            print(f"   - '{c[0]}'")
        
        # Show expected enum values
        print("\n🎯 Expected enum values:")
        print("   Transaction Types: 'FUNDS_GIVEN', 'FUNDS_RETURNED', 'PARTIAL_RETURN'")
        print("   Statuses: 'PENDING', 'COMPLETED', 'CANCELLED'")
        print("   Channels: 'CASH', 'BANK_TRANSFER'")
        
        # Check for mismatches
        print("\n⚠️  Potential issues:")
        
        # Check transaction types
        cursor.execute("SELECT DISTINCT transaction_type FROM transactions")
        db_types = [t[0] for t in cursor.fetchall()]
        expected_types = ['FUNDS_GIVEN', 'FUNDS_RETURNED', 'PARTIAL_RETURN']

        for db_type in db_types:
            if db_type not in expected_types:
                print(f"   ❌ Transaction type '{db_type}' is not in expected values: {expected_types}")

        # Check statuses
        cursor.execute("SELECT DISTINCT status FROM transactions")
        db_statuses = [s[0] for s in cursor.fetchall()]
        expected_statuses = ['PENDING', 'COMPLETED', 'CANCELLED', 'DISPUTED']

        for db_status in db_statuses:
            if db_status not in expected_statuses:
                print(f"   ❌ Status '{db_status}' is not in expected values: {expected_statuses}")

        # Check channels
        cursor.execute("SELECT DISTINCT channel FROM transactions")
        db_channels = [c[0] for c in cursor.fetchall()]
        expected_channels = ['CASH', 'BANK_TRANSFER']

        for db_channel in db_channels:
            if db_channel not in expected_channels:
                print(f"   ❌ Channel '{db_channel}' is not in expected values: {expected_channels}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_enum_values()
