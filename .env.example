# Database Configuration
DATABASE_URL=sqlite:///./bureau_de_change.db
# For PostgreSQL: postgresql://username:password@localhost/bureau_de_change

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000/api

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
