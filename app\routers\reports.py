from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime, timedelta

from models.database import get_db
from models.transaction import Transaction, TransactionType
from models.partner import Partner
from models.user import User
from utils.dependencies import get_current_user
from services.balance_calculator import BalanceCalculator

router = APIRouter()

@router.get("/dashboard")
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Active partners count
    active_partners = db.query(Partner).filter(Partner.status == "active").count()
    
    # Total outstanding
    total_given = db.query(func.sum(Transaction.usd_equivalent)).filter(
        Transaction.transaction_type == TransactionType.FUNDS_GIVEN,
        Transaction.status == "completed"
    ).scalar() or 0
    
    total_returned = db.query(func.sum(Transaction.usd_equivalent)).filter(
        Transaction.transaction_type.in_([TransactionType.FUNDS_RETURNED, TransactionType.PARTIAL_RETURN]),
        Transaction.status == "completed"
    ).scalar() or 0
    
    outstanding_balance = total_given - total_returned

    # Get multi-currency outstanding balances
    multi_currency_balances = BalanceCalculator.calculate_multi_currency_outstanding(db)

    # Today's transactions
    today = datetime.now().date()
    today_transactions = db.query(Transaction).filter(
        func.date(Transaction.created_at) == today
    ).count()

    # Partners with pending balances
    pending_partners = db.query(Partner.id).join(Transaction).filter(
        Transaction.transaction_type == TransactionType.FUNDS_GIVEN,
        Transaction.status == "completed"
    ).distinct().count()

    return {
        "active_partners": active_partners,
        "total_outstanding": float(outstanding_balance),
        "outstanding_balances": multi_currency_balances,
        "today_transactions": today_transactions,
        "pending_partners": pending_partners,
        "total_given": float(total_given),
        "total_returned": float(total_returned)
    }

@router.get("/daily")
async def get_daily_report(
    date: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    if date:
        target_date = datetime.strptime(date, "%Y-%m-%d").date()
    else:
        target_date = datetime.now().date()
    
    transactions = db.query(Transaction).filter(
        func.date(Transaction.created_at) == target_date
    ).all()
    
    return {
        "date": target_date.isoformat(),
        "transactions": transactions,
        "total_amount": sum(float(t.usd_equivalent) for t in transactions),
        "transaction_count": len(transactions)
    }
