#!/usr/bin/env python3
"""
Test endpoints on port 8002 after fixing enum values
"""

import requests

def test_endpoints():
    """Test all endpoints"""
    print("🧪 Testing endpoints on port 8002 after enum fix...")
    
    try:
        # Test auth
        print("📤 Testing auth...")
        auth_response = requests.post("http://localhost:8002/api/auth/token", data={
            "username": "admin",
            "password": "admin123"
        })
        print(f"   Status: {auth_response.status_code}")
        
        if auth_response.status_code == 200:
            token = auth_response.json()["access_token"]
            print("   ✅ Auth successful")
            
            headers = {"Authorization": f"Bearer {token}"}
            
            # Test partners endpoint
            print("\n📤 Testing partners endpoint...")
            partners_response = requests.get("http://localhost:8002/api/partners/", headers=headers)
            print(f"   Status: {partners_response.status_code}")
            
            if partners_response.status_code == 200:
                partners = partners_response.json()
                print(f"   ✅ Partners: {len(partners)} found")
                if partners:
                    print(f"   Sample: {partners[0].get('name')}")
                    print(f"   Outstanding balance: {partners[0].get('outstanding_balance', 'N/A')}")
            else:
                print(f"   ❌ Error: {partners_response.text}")
            
            # Test transactions endpoint
            print("\n📤 Testing transactions endpoint...")
            transactions_response = requests.get("http://localhost:8002/api/transactions/", headers=headers)
            print(f"   Status: {transactions_response.status_code}")
            
            if transactions_response.status_code == 200:
                transactions = transactions_response.json()
                print(f"   ✅ Transactions: {len(transactions)} found")
                if transactions:
                    trans = transactions[0]
                    print(f"   Sample: {trans.get('amount')} {trans.get('currency')} - {trans.get('channel')}")
            else:
                print(f"   ❌ Error: {transactions_response.text}")
            
            # Test dashboard endpoint
            print("\n📤 Testing dashboard endpoint...")
            dashboard_response = requests.get("http://localhost:8002/api/reports/dashboard", headers=headers)
            print(f"   Status: {dashboard_response.status_code}")
            
            if dashboard_response.status_code == 200:
                dashboard = dashboard_response.json()
                print(f"   ✅ Dashboard data retrieved")
                print(f"   Outstanding balances: {dashboard.get('outstanding_balances', {})}")
            else:
                print(f"   ❌ Error: {dashboard_response.text}")
                
        else:
            print(f"   ❌ Auth failed: {auth_response.text}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_endpoints()
