from sqlalchemy import Column, Integer, String, Numeric, DateTime, Enum, ForeignKey, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .database import Base
import enum

class TransactionType(str, enum.Enum):
    FUNDS_GIVEN = "FUNDS_GIVEN"
    FUNDS_RETURNED = "FUNDS_RETURNED"
    PARTIAL_RETURN = "PARTIAL_RETURN"

class TransactionStatus(str, enum.Enum):
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    DISPUTED = "DISPUTED"

class TransactionChannel(str, enum.Enum):
    CASH = "CASH"
    BANK_TRANSFER = "BANK_TRANSFER"

class Transaction(Base):
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    partner_id = Column(Integer, ForeignKey("partners.id"), nullable=False)
    transaction_type = Column(Enum(TransactionType), nullable=False)
    amount = Column(Numeric(15, 2), nullable=False)
    currency = Column(String(3), nullable=False)
    exchange_rate = Column(Numeric(12, 4), nullable=True)
    usd_equivalent = Column(Numeric(15, 2), nullable=False)
    reference_number = Column(String(50), unique=True, nullable=True)
    status = Column(Enum(TransactionStatus), default=TransactionStatus.PENDING)
    channel = Column(Enum(TransactionChannel), nullable=False, default=TransactionChannel.CASH)
    notes = Column(Text, nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    partner = relationship("Partner", back_populates="transactions")
    creator = relationship("User", foreign_keys=[created_by])
