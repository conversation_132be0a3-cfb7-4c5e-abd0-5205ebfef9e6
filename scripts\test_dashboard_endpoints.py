#!/usr/bin/env python3
"""
Quick test of dashboard endpoints after schema fix
"""

import requests
import json

def test_endpoints():
    """Test the main dashboard endpoints"""
    print("🧪 Testing dashboard endpoints after schema fix...")
    
    # Get auth token
    print("\n🔐 Getting auth token...")
    auth_response = requests.post("http://localhost:8000/api/auth/token", data={
        "username": "admin",
        "password": "admin123"
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Auth failed: {auth_response.status_code}")
        return
    
    token = auth_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test endpoints
    endpoints = [
        "/api/partners",
        "/api/transactions", 
        "/api/reports/dashboard"
    ]
    
    for endpoint in endpoints:
        print(f"\n📤 Testing {endpoint}")
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", headers=headers)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    print(f"   ✅ Success - {len(data)} items")
                else:
                    print(f"   ✅ Success - Response keys: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
            else:
                print(f"   ❌ Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    test_endpoints()
