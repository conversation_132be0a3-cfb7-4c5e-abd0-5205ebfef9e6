#!/usr/bin/env python3
"""
Migration script to add channel field to existing transactions
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))

from sqlalchemy import text
from models.database import engine, SessionLocal
from models.transaction import Transaction

def migrate_add_channel():
    """Add channel column to transactions table and set default values"""
    print("🔄 Adding channel field to transactions table...")
    
    try:
        # Create database session
        db = SessionLocal()
        
        try:
            # Check if column already exists
            result = db.execute(text("""
                SELECT COUNT(*) as count 
                FROM pragma_table_info('transactions') 
                WHERE name = 'channel'
            """))
            
            column_exists = result.fetchone()[0] > 0
            
            if column_exists:
                print("✅ Channel column already exists in transactions table")
                return
            
            # Add the channel column with default value
            db.execute(text("""
                ALTER TABLE transactions 
                ADD COLUMN channel VARCHAR(20) DEFAULT 'cash' NOT NULL
            """))
            
            # Update existing transactions to have 'cash' as default channel
            db.execute(text("""
                UPDATE transactions 
                SET channel = 'cash' 
                WHERE channel IS NULL
            """))
            
            db.commit()
            print("✅ Successfully added channel field to transactions table")
            print("✅ Set default channel value to 'cash' for existing transactions")
            
        except Exception as e:
            db.rollback()
            print(f"❌ Error during migration: {e}")
            raise
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        sys.exit(1)

def main():
    """Main migration function"""
    print("🚀 Running Channel Field Migration")
    print("=" * 50)
    
    try:
        migrate_add_channel()
        print("\n✅ Migration completed successfully!")
        print("\nNext steps:")
        print("1. Restart the FastAPI server to pick up the new field")
        print("2. New transactions will now include channel selection")
        print("3. Existing transactions have been set to 'cash' channel")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
