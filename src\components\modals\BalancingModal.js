import React, { useState } from 'react';
import { X, Calculator } from 'lucide-react';

const BalancingModal = ({ partners, onSubmit, onClose }) => {
  const [formData, setFormData] = useState({
    partner_id: '',
    usd_amount: '',
    ngn_amount: '',
    current_rate: '1600'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const calculateTotal = () => {
    const usd = parseFloat(formData.usd_amount) || 0;
    const ngn = parseFloat(formData.ngn_amount) || 0;
    const rate = parseFloat(formData.current_rate) || 1;
    
    return usd + (ngn / rate);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Validate form
      if (!formData.partner_id) {
        throw new Error('Please select a partner');
      }

      const usdAmount = parseFloat(formData.usd_amount) || 0;
      const ngnAmount = parseFloat(formData.ngn_amount) || 0;
      const currentRate = parseFloat(formData.current_rate) || 0;

      if (usdAmount <= 0 && ngnAmount <= 0) {
        throw new Error('Please enter at least one amount (USD or NGN)');
      }

      if (ngnAmount > 0 && currentRate <= 0) {
        throw new Error('Please enter a valid exchange rate for NGN conversion');
      }

      const balanceData = {
        partner_id: parseInt(formData.partner_id),
        usd_amount: usdAmount,
        ngn_amount: ngnAmount,
        current_rate: currentRate
      };

      await onSubmit(balanceData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const selectedPartner = partners.find(p => p.id === parseInt(formData.partner_id));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Process Balance Return</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Partner *
            </label>
            <select
              name="partner_id"
              value={formData.partner_id}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select a partner</option>
              {partners.filter(p => p.current_balance > 0).map(partner => (
                <option key={partner.id} value={partner.id}>
                  {partner.name} (Balance: ${partner.current_balance?.toLocaleString() || 0})
                </option>
              ))}
            </select>
          </div>

          {selectedPartner && (
            <div className="bg-blue-50 p-3 rounded-md">
              <p className="text-sm text-blue-800">
                <strong>Current Balance:</strong> ${selectedPartner.current_balance?.toLocaleString() || 0}
              </p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                USD Amount
              </label>
              <input
                type="number"
                name="usd_amount"
                value={formData.usd_amount}
                onChange={handleChange}
                step="0.01"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                NGN Amount
              </label>
              <input
                type="number"
                name="ngn_amount"
                value={formData.ngn_amount}
                onChange={handleChange}
                step="0.01"
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Current Exchange Rate (NGN/USD)
            </label>
            <input
              type="number"
              name="current_rate"
              value={formData.current_rate}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="1600"
            />
          </div>

          {(formData.usd_amount || formData.ngn_amount) && (
            <div className="bg-green-50 p-3 rounded-md">
              <div className="flex items-center">
                <Calculator className="h-4 w-4 text-green-600 mr-2" />
                <span className="text-sm text-green-800">
                  <strong>Total USD Equivalent:</strong> ${calculateTotal().toFixed(2)}
                </span>
              </div>
            </div>
          )}

          <div className="bg-yellow-50 p-3 rounded-md">
            <p className="text-xs text-yellow-800">
              <strong>Note:</strong> This will create a return transaction and update the partner's balance using FIFO allocation.
            </p>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <>
                  <Calculator className="h-4 w-4 mr-2" />
                  Process Return
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BalancingModal;
