from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime
from decimal import Decimal
from models.transaction import TransactionType, TransactionStatus, TransactionChannel
from .partner import Partner

class TransactionBase(BaseModel):
    partner_id: int
    transaction_type: TransactionType
    amount: Decimal
    currency: str
    exchange_rate: Optional[Decimal] = None
    channel: TransactionChannel = TransactionChannel.CASH
    notes: Optional[str] = None

class TransactionCreate(TransactionBase):
    @validator('transaction_type', pre=True)
    def validate_transaction_type(cls, v):
        # Handle both string and enum values for backward compatibility
        if isinstance(v, str):
            type_mapping = {
                'funds_given': 'FUNDS_GIVEN',
                'funds_returned': 'FUNDS_RETURNED',
                'partial_return': 'PARTIAL_RETURN',
                'FUNDS_GIVEN': 'FUNDS_GIVEN',
                'FUNDS_RETURNED': 'FUNDS_RETURNED',
                'PARTIAL_RETURN': 'PARTIAL_RETURN'
            }
            if v not in type_mapping:
                raise ValueError(f'Transaction type must be one of {list(type_mapping.keys())}')
            return type_mapping[v]
        return v

    @validator('currency')
    def validate_currency(cls, v):
        allowed_currencies = ['NGN', 'USD', 'GBP', 'EUR']
        if v not in allowed_currencies:
            raise ValueError(f'Currency must be one of {allowed_currencies}')
        return v

    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('Amount must be greater than 0')
        return v

    @validator('channel', pre=True)
    def validate_channel(cls, v):
        # Handle both string and enum values
        if isinstance(v, str):
            # Accept both lowercase (for backward compatibility) and uppercase values
            channel_mapping = {
                'cash': 'CASH',
                'bank_transfer': 'BANK_TRANSFER',
                'CASH': 'CASH',
                'BANK_TRANSFER': 'BANK_TRANSFER'
            }
            if v not in channel_mapping:
                raise ValueError(f'Channel must be one of {list(channel_mapping.keys())}')
            return channel_mapping[v]
        return v

class TransactionUpdate(BaseModel):
    status: Optional[TransactionStatus] = None
    notes: Optional[str] = None

class Transaction(TransactionBase):
    id: int
    usd_equivalent: Decimal
    reference_number: Optional[str]
    status: TransactionStatus
    channel: TransactionChannel
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TransactionWithPartner(Transaction):
    partner: Partner
