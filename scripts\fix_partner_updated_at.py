#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix Partner records with NULL updated_at values.
This script updates all Partner records where updated_at is NULL to use created_at value.
"""

import sqlite3
import os

def fix_partner_updated_at():
    """Fix Partner records with NULL updated_at values using raw SQL"""

    # Database path
    db_path = os.path.join(os.path.dirname(__file__), '..', 'bureau_de_change.db')

    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check how many partners have NULL updated_at
        cursor.execute("SELECT COUNT(*) FROM partners WHERE updated_at IS NULL")
        count = cursor.fetchone()[0]
        print(f"Found {count} partners with NULL updated_at")

        if count == 0:
            print("No partners need fixing.")
            return

        # Update partners with NULL updated_at to use created_at value
        cursor.execute("""
            UPDATE partners
            SET updated_at = created_at
            WHERE updated_at IS NULL
        """)

        # Also fix transactions table if it has the same issue
        cursor.execute("SELECT COUNT(*) FROM transactions WHERE updated_at IS NULL")
        trans_count = cursor.fetchone()[0]
        print(f"Found {trans_count} transactions with NULL updated_at")

        if trans_count > 0:
            cursor.execute("""
                UPDATE transactions
                SET updated_at = created_at
                WHERE updated_at IS NULL
            """)

        # Commit the changes
        conn.commit()
        print(f"Successfully updated {count} partner records and {trans_count} transaction records.")

    except Exception as e:
        print(f"Error fixing updated_at: {e}")
        raise
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("Fixing Partner records with NULL updated_at values...")
    fix_partner_updated_at()
    print("Done!")
