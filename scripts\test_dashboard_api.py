#!/usr/bin/env python3
"""
Test script to debug dashboard API endpoints
"""

import requests
import json

def get_auth_token():
    """Get authentication token"""
    print("🔐 Getting authentication token...")
    
    url = "http://localhost:8000/api/auth/token"
    data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(url, data=data)
        if response.status_code == 200:
            result = response.json()
            token = result.get('access_token')
            print(f"✅ Token obtained: {token[:20]}...")
            return token
        else:
            print(f"❌ Failed to get token: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error getting token: {e}")
        return None

def test_dashboard_endpoints(token):
    """Test all dashboard-related endpoints"""
    print("\n🧪 Testing dashboard endpoints...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    endpoints = [
        "/api/partners",
        "/api/partners/",
        "/api/transactions", 
        "/api/transactions/",
        "/api/reports/dashboard"
    ]
    
    results = {}
    
    for endpoint in endpoints:
        url = f"http://localhost:8000{endpoint}"
        try:
            print(f"\n📤 GET {url}")
            response = requests.get(url, headers=headers)
            
            print(f"📥 Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Success - Data length: {len(data) if isinstance(data, list) else 'N/A'}")
                    if isinstance(data, list) and len(data) > 0:
                        print(f"   Sample item keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'N/A'}")
                    elif isinstance(data, dict):
                        print(f"   Response keys: {list(data.keys())}")
                    results[endpoint] = {"status": "success", "data": data}
                except json.JSONDecodeError:
                    print(f"✅ Success - Non-JSON response: {response.text[:100]}...")
                    results[endpoint] = {"status": "success", "data": response.text}
            else:
                print(f"❌ Failed - {response.text}")
                results[endpoint] = {"status": "failed", "error": response.text}
                
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection error")
            results[endpoint] = {"status": "connection_error"}
        except Exception as e:
            print(f"❌ Error: {e}")
            results[endpoint] = {"status": "error", "error": str(e)}
    
    return results

def test_specific_issues(token):
    """Test for specific known issues"""
    print("\n🔍 Testing for specific issues...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test if trailing slash matters
    print("\n📋 Testing trailing slash behavior:")
    for endpoint in ["/api/partners", "/api/partners/"]:
        url = f"http://localhost:8000{endpoint}"
        try:
            response = requests.get(url, headers=headers)
            print(f"   {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   {endpoint}: Error - {e}")
    
    # Test CORS headers
    print("\n📋 Testing CORS headers:")
    try:
        response = requests.get("http://localhost:8000/api/partners", headers={
            **headers,
            "Origin": "http://localhost:3000"
        })
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        print(f"   CORS headers: {cors_headers}")
    except Exception as e:
        print(f"   CORS test error: {e}")

def main():
    """Run all tests"""
    print("🚀 Testing Dashboard API Endpoints")
    print("=" * 50)
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token!")
        return
    
    # Test dashboard endpoints
    results = test_dashboard_endpoints(token)
    
    # Test specific issues
    test_specific_issues(token)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    
    success_count = sum(1 for r in results.values() if r["status"] == "success")
    total_count = len(results)
    
    print(f"✅ Successful endpoints: {success_count}/{total_count}")
    
    failed_endpoints = [ep for ep, r in results.items() if r["status"] != "success"]
    if failed_endpoints:
        print(f"❌ Failed endpoints: {failed_endpoints}")
        
        print("\n🔧 Troubleshooting suggestions:")
        print("1. Check if the backend server is running on port 8000")
        print("2. Verify database has data (partners, transactions)")
        print("3. Check for any server errors in the backend logs")
        print("4. Ensure all database tables are created and populated")
    else:
        print("🎉 All endpoints are working!")
        print("💡 The issue might be in the frontend API calls or error handling")

if __name__ == "__main__":
    main()
