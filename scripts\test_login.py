#!/usr/bin/env python3
"""
Test script to debug login issues
"""

import sys
from pathlib import Path
import json

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))

from sqlalchemy.orm import Session
from models.database import SessionLocal
from models.user import User
from utils.security import verify_password, get_password_hash, create_access_token, verify_token
from passlib.context import Crypt<PERSON>ontext

def test_user_exists():
    """Check if admin user exists and password is correct"""
    print("🔍 Checking if admin user exists...")
    
    db = SessionLocal()
    try:
        user = db.query(User).filter(User.username == "admin").first()
        if user:
            print(f"✅ Admin user found: {user.username} (ID: {user.id})")
            print(f"   - Email: {user.email}")
            print(f"   - Is Active: {user.is_active}")
            print(f"   - Password Hash: {user.password_hash[:20]}...")
            return user
        else:
            print("❌ Admin user not found!")
            return None
    finally:
        db.close()

def test_password_verification():
    """Test password verification"""
    print("\n🔐 Testing password verification...")
    
    db = SessionLocal()
    try:
        user = db.query(User).filter(User.username == "admin").first()
        if not user:
            print("❌ No admin user found!")
            return False
        
        # Test with the default password
        test_password = "admin123"
        
        # Create password context
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Verify password
        is_valid = pwd_context.verify(test_password, user.password_hash)
        
        if is_valid:
            print(f"✅ Password verification successful for '{test_password}'")
            return True
        else:
            print(f"❌ Password verification failed for '{test_password}'")
            
            # Try to create a new hash and compare
            new_hash = pwd_context.hash(test_password)
            print(f"   - New hash would be: {new_hash[:20]}...")
            print(f"   - Current hash is: {user.password_hash[:20]}...")
            
            return False
    except Exception as e:
        print(f"❌ Error during password verification: {e}")
        return False
    finally:
        db.close()

def test_auth_service():
    """Test the auth service directly"""
    print("\n🧪 Testing Authentication...")

    db = SessionLocal()
    try:
        # Test authentication
        user = db.query(User).filter(User.username == "admin").first()
        if not user:
            print("❌ Admin user not found!")
            return False

        # Test password verification
        password_ok = verify_password("admin123", user.password_hash)
        if not password_ok:
            print("❌ Password verification failed!")
            return False

        print("✅ Password verification successful!")

        # Test token creation
        token = create_access_token(data={"sub": user.username})
        print(f"✅ Token created successfully! Token: {token[:20]}...")

        # Test token verification
        user_data = verify_token(token)
        if user_data:
            print(f"✅ Token verification successful! User: {user_data.get('sub')}")
            return True
        else:
            print("❌ Token verification failed!")
            return False

    except Exception as e:
        print(f"❌ Error in authentication: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def reset_admin_password():
    """Reset admin password to default"""
    print("\n🔄 Resetting admin password...")
    
    db = SessionLocal()
    try:
        user = db.query(User).filter(User.username == "admin").first()
        if not user:
            print("❌ No admin user found!")
            return False
        
        # Create new password hash
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        new_password = "admin123"
        new_hash = pwd_context.hash(new_password)
        
        # Update user password
        user.password_hash = new_hash
        db.commit()
        
        print(f"✅ Admin password reset to '{new_password}'")
        print(f"   - New hash: {new_hash[:20]}...")
        
        return True
    except Exception as e:
        print(f"❌ Error resetting password: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """Run all tests"""
    print("🚀 Testing Login and Authentication")
    print("=" * 50)
    
    # Check if user exists
    user = test_user_exists()
    if not user:
        print("\n❌ Cannot proceed without admin user!")
        return False
    
    # Test password verification
    password_ok = test_password_verification()
    
    if not password_ok:
        print("\n🔄 Password verification failed, attempting to reset...")
        reset_ok = reset_admin_password()
        if reset_ok:
            password_ok = test_password_verification()
    
    if password_ok:
        # Test auth service
        auth_ok = test_auth_service()
        
        print("\n" + "=" * 50)
        if auth_ok:
            print("🎉 All authentication tests passed!")
            print("💡 You should now be able to login with:")
            print("   Username: admin")
            print("   Password: admin123")
        else:
            print("⚠️  Authentication service has issues.")
        
        return auth_ok
    else:
        print("\n" + "=" * 50)
        print("⚠️  Password verification still failing after reset.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
