#!/usr/bin/env python3
"""
Test script to debug transaction creation issues
"""

import sys
from pathlib import Path
import json

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))

from sqlalchemy.orm import Session
from models.database import SessionLocal
from models.transaction import Transaction, TransactionType, TransactionChannel
from models.partner import Partner
from models.user import User
from services.transaction_processor import TransactionProcessor
from schemas.transaction import TransactionCreate
from decimal import Decimal

def test_transaction_creation():
    """Test transaction creation with the new channel field"""
    print("🧪 Testing transaction creation...")
    
    db = SessionLocal()
    try:
        # Get a partner for testing
        partner = db.query(Partner).first()
        if not partner:
            print("❌ No partners found. Please run init_db.py first.")
            return False
        
        # Get a user for testing
        user = db.query(User).first()
        if not user:
            print("❌ No users found. Please run init_db.py first.")
            return False
        
        print(f"✅ Found partner: {partner.name} (ID: {partner.id})")
        print(f"✅ Found user: {user.username} (ID: {user.id})")
        
        # Test data similar to what frontend would send
        test_data = {
            "partner_id": partner.id,
            "transaction_type": "funds_given",
            "amount": 500000.0,
            "currency": "NGN",
            "exchange_rate": 1700.0,
            "channel": "cash",
            "notes": "Test transaction from script"
        }
        
        print(f"📤 Test data: {json.dumps(test_data, indent=2)}")
        
        # Create TransactionCreate object
        try:
            transaction_data = TransactionCreate(**test_data)
            print("✅ TransactionCreate object created successfully")
            print(f"   - Channel: {transaction_data.channel}")
            print(f"   - Channel type: {type(transaction_data.channel)}")
        except Exception as e:
            print(f"❌ Failed to create TransactionCreate object: {e}")
            return False
        
        # Create the transaction
        try:
            transaction = TransactionProcessor.create_transaction(db, transaction_data, user.id)
            print("✅ Transaction created successfully!")
            print(f"   - ID: {transaction.id}")
            print(f"   - Reference: {transaction.reference_number}")
            print(f"   - Channel: {transaction.channel}")
            print(f"   - Amount: {transaction.amount} {transaction.currency}")
            print(f"   - USD Equivalent: ${transaction.usd_equivalent}")
            return True
        except Exception as e:
            print(f"❌ Failed to create transaction: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_schema_validation():
    """Test schema validation with different input types"""
    print("\n🧪 Testing schema validation...")
    
    test_cases = [
        {
            "name": "String channel",
            "data": {
                "partner_id": 1,
                "transaction_type": TransactionType.FUNDS_GIVEN,
                "amount": Decimal("1000"),
                "currency": "NGN",
                "exchange_rate": Decimal("1700"),
                "channel": "cash",
                "notes": "Test with string channel"
            }
        },
        {
            "name": "Enum channel",
            "data": {
                "partner_id": 1,
                "transaction_type": TransactionType.FUNDS_GIVEN,
                "amount": Decimal("1000"),
                "currency": "NGN",
                "exchange_rate": Decimal("1700"),
                "channel": TransactionChannel.CASH,
                "notes": "Test with enum channel"
            }
        },
        {
            "name": "Invalid channel",
            "data": {
                "partner_id": 1,
                "transaction_type": TransactionType.FUNDS_GIVEN,
                "amount": Decimal("1000"),
                "currency": "NGN",
                "exchange_rate": Decimal("1700"),
                "channel": "invalid_channel",
                "notes": "Test with invalid channel"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 Testing: {test_case['name']}")
        try:
            transaction_data = TransactionCreate(**test_case['data'])
            print(f"   ✅ Success - Channel: {transaction_data.channel} (type: {type(transaction_data.channel)})")
        except Exception as e:
            print(f"   ❌ Failed: {e}")

def main():
    """Run all tests"""
    print("🚀 Testing Transaction Creation and Schema Validation")
    print("=" * 60)
    
    # Test schema validation first
    test_schema_validation()
    
    # Test actual transaction creation
    print("\n" + "=" * 60)
    success = test_transaction_creation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! Transaction creation is working correctly.")
    else:
        print("⚠️  Transaction creation test failed. Check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
