#!/usr/bin/env python3
"""
Test script to simulate frontend API calls exactly as the frontend would make them
"""

import requests
import json

def test_frontend_login():
    """Test login exactly as the frontend does it"""
    print("🧪 Testing frontend-style login...")
    
    url = "http://localhost:8000/api/auth/token"
    
    # Create FormData exactly like the frontend
    files = {
        'username': (None, 'admin'),
        'password': (None, 'admin123')
    }
    
    # Headers that a browser would send
    headers = {
        'Accept': 'application/json',
        'Origin': 'http://localhost:3000',
        'Referer': 'http://localhost:3000/login',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        print(f"📤 POST {url}")
        print(f"📋 Form Data: username=admin, password=admin123")
        print(f"📋 Headers: {headers}")
        
        response = requests.post(url, files=files, headers=headers)
        
        print(f"📥 Status Code: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Login successful!")
            print(f"   Token: {result.get('access_token', '')[:20]}...")
            print(f"   Token Type: {result.get('token_type')}")
            return result.get('access_token')
        else:
            print(f"❌ Login failed!")
            print(f"   Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        print("   This suggests the server is not reachable from the frontend perspective")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_cors_preflight():
    """Test CORS preflight request"""
    print("\n🧪 Testing CORS preflight...")
    
    url = "http://localhost:8000/api/auth/token"
    
    headers = {
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'content-type',
        'Origin': 'http://localhost:3000'
    }
    
    try:
        print(f"📤 OPTIONS {url}")
        print(f"📋 Headers: {headers}")
        
        response = requests.options(url, headers=headers)
        
        print(f"📥 Status Code: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ CORS preflight successful!")
            return True
        else:
            print("❌ CORS preflight failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_server_reachability():
    """Test basic server reachability"""
    print("\n🧪 Testing server reachability...")
    
    urls = [
        "http://localhost:8000/",
        "http://localhost:8000/health",
        "http://localhost:8000/docs"
    ]
    
    for url in urls:
        try:
            print(f"📤 GET {url}")
            response = requests.get(url, timeout=5)
            print(f"   ✅ Status: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection failed")
        except requests.exceptions.Timeout:
            print(f"   ❌ Timeout")
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    """Run all tests"""
    print("🚀 Testing Frontend API Connectivity")
    print("=" * 50)
    
    # Test basic server reachability
    test_server_reachability()
    
    # Test CORS preflight
    cors_ok = test_cors_preflight()
    
    # Test login
    token = test_frontend_login()
    
    print("\n" + "=" * 50)
    if token and cors_ok:
        print("🎉 All frontend API tests passed!")
        print("💡 The backend should be accessible from the frontend.")
        print("💡 If login still fails in the browser, check:")
        print("   - Browser developer console for errors")
        print("   - Network tab for failed requests")
        print("   - Any browser security restrictions")
    else:
        print("⚠️  Some tests failed.")
        if not cors_ok:
            print("   - CORS configuration might be the issue")
        if not token:
            print("   - Login endpoint is not working properly")

if __name__ == "__main__":
    main()
