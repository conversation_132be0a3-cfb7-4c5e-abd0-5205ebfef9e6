#!/usr/bin/env python3
"""
Check database content and enum values
"""

import sqlite3
import os

def check_database():
    """Check database content"""
    print("🔍 Checking database content...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'bureau_de_change.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check transactions
        print("\n📋 Transactions:")
        cursor.execute("SELECT id, transaction_type, status, channel FROM transactions LIMIT 5")
        transactions = cursor.fetchall()
        
        for trans in transactions:
            print(f"   ID: {trans[0]}, Type: {trans[1]}, Status: {trans[2]}, Channel: {trans[3]}")
        
        # Check unique values
        print("\n📊 Unique transaction_type values:")
        cursor.execute("SELECT DISTINCT transaction_type FROM transactions")
        types = cursor.fetchall()
        for t in types:
            print(f"   - {t[0]}")
        
        print("\n📊 Unique status values:")
        cursor.execute("SELECT DISTINCT status FROM transactions")
        statuses = cursor.fetchall()
        for s in statuses:
            print(f"   - {s[0]}")
        
        print("\n📊 Unique channel values:")
        cursor.execute("SELECT DISTINCT channel FROM transactions")
        channels = cursor.fetchall()
        for c in channels:
            print(f"   - {c[0]}")
        
        # Check partners
        print("\n📋 Partners:")
        cursor.execute("SELECT id, name FROM partners LIMIT 5")
        partners = cursor.fetchall()
        
        for partner in partners:
            print(f"   ID: {partner[0]}, Name: {partner[1]}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
