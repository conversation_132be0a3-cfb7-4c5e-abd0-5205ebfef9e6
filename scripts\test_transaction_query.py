#!/usr/bin/env python3
"""
Test transaction queries directly
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from models.database import get_db
from models.transaction import Transaction
from sqlalchemy.orm import Session

def test_transaction_query():
    """Test basic transaction query"""
    print("🧪 Testing transaction query...")
    
    try:
        # Get database session
        db = next(get_db())
        
        # Try to query transactions
        print("📤 Querying transactions...")
        transactions = db.query(Transaction).limit(5).all()
        
        print(f"✅ Success! Found {len(transactions)} transactions")
        
        if transactions:
            print("📋 Sample transaction:")
            trans = transactions[0]
            print(f"   ID: {trans.id}")
            print(f"   Partner ID: {trans.partner_id}")
            print(f"   Type: {trans.transaction_type}")
            print(f"   Amount: {trans.amount}")
            print(f"   Currency: {trans.currency}")
            print(f"   Channel: {getattr(trans, 'channel', 'NOT_FOUND')}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_transaction_query()
