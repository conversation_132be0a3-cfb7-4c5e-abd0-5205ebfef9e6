@echo off
REM Quick fix script for current terminal issues

echo ========================================
echo 171 Bureau De Change - Quick Fix Script
echo ========================================

REM Set UTF-8 encoding
chcp 65001 >nul

echo [1/4] Navigating to project root...
cd /d "C:\Users\<USER>\others\bdc_app"

echo [2/4] Activating environments...
call conda activate bdc
call venv\Scripts\activate.bat

echo [3/4] Setting Python path...
set PYTHONPATH=%CD%

echo [4/4] Testing backend startup...
echo Starting backend server (press Ctrl+C to stop)...
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

pause
