#!/usr/bin/env python3
"""
Check database tables and content
"""

import sqlite3
import os

def check_database():
    """Check database tables and content"""
    print("🔍 Checking database tables...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'bureau_de_change.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # List all tables
        print("\n📋 Tables in database:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        for table in tables:
            print(f"   - {table[0]}")
        
        # Check each table's content
        for table in tables:
            table_name = table[0]
            print(f"\n📊 Table: {table_name}")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   Rows: {count}")
            
            if count > 0:
                # Show first few rows
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                rows = cursor.fetchall()
                
                # Get column names
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]
                print(f"   Columns: {columns}")
                
                print("   Sample data:")
                for row in rows:
                    print(f"     {row}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
