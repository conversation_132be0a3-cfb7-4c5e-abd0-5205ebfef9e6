@echo off
REM 171 Bureau De Change - Service Startup Script
REM This script starts all required services for the application

echo ========================================
echo 171 Bureau De Change - Starting Services
echo ========================================

REM Set console encoding to UTF-8 to handle Unicode characters
chcp 65001 >nul

REM Check if we're in the correct directory
if not exist "app\main.py" (
    echo ERROR: Please run this script from the project root directory
    echo Current directory: %CD%
    echo Expected files: app\main.py, package.json
    pause
    exit /b 1
)

REM Activate conda environment
echo [1/6] Activating conda environment...
call conda activate bdc
if errorlevel 1 (
    echo ERROR: Failed to activate conda environment 'bdc'
    echo Please ensure conda environment 'bdc' exists
    echo Run: conda create --name bdc python=3.12
    pause
    exit /b 1
)

REM Activate Python virtual environment
echo [2/6] Activating Python virtual environment...
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
) else (
    echo WARNING: Python virtual environment not found
    echo Creating virtual environment...
    python -m venv venv
    call venv\Scripts\activate.bat
    pip install -r requirements.txt
)

REM Check if database is initialized
echo [3/6] Checking database...
if not exist "bureau_de_change.db" (
    echo Initializing database...
    python scripts\init_db.py
    if errorlevel 1 (
        echo ERROR: Database initialization failed
        pause
        exit /b 1
    )
)

REM Check if Node.js dependencies are installed
echo [4/6] Checking Node.js dependencies...
if not exist "node_modules" (
    echo Installing Node.js dependencies...
    npm install
    if errorlevel 1 (
        echo ERROR: npm install failed
        echo Trying with increased timeout...
        npm install --fetch-timeout=300000 --fetch-retry-mintimeout=20000
        if errorlevel 1 (
            echo ERROR: npm install failed even with increased timeout
            pause
            exit /b 1
        )
    )
)

echo [5/6] Starting services...

REM Start backend server in a new window
echo Starting FastAPI backend server...
start "BDC Backend" cmd /k "conda activate bdc && venv\Scripts\activate && set PYTHONPATH=%CD% && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend server in a new window
echo Starting React frontend server...
start "BDC Frontend" cmd /k "npm start"

echo [6/6] Services started!
echo ========================================
echo Services Status:
echo ✓ Backend:  http://localhost:8000
echo ✓ Frontend: http://localhost:3000
echo ✓ API Docs: http://localhost:8000/docs
echo ========================================
echo.
echo Default login credentials:
echo Username: admin
echo Password: admin123
echo.
echo Press any key to open the application in your browser...
pause >nul

REM Open application in default browser
start http://localhost:3000

echo.
echo Services are running in separate windows.
echo Close those windows to stop the services.
echo.
pause
