#!/usr/bin/env python3
"""
Production setup script for 171 Bureau De Change
Sets up the production environment
"""

import os
import sys
import subprocess
import secrets
from pathlib import Path

def run_command(command, description, cwd=None):
    """Run a command and handle errors"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            cwd=cwd,
            capture_output=True,
            text=True
        )
        print(f"✓ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def generate_secret_key():
    """Generate a secure secret key"""
    return secrets.token_urlsafe(32)

def create_production_env():
    """Create production environment file"""
    print("⚙️ Creating production environment configuration...")
    
    # Generate secure secret key
    secret_key = generate_secret_key()
    
    # Production environment template
    prod_env = f"""# Production Environment Configuration
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost/bureau_de_change

# Security (CHANGE THESE VALUES!)
SECRET_KEY={secret_key}
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=False

# Frontend Configuration
REACT_APP_API_URL=https://yourdomain.com/api

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
"""
    
    try:
        with open(".env.production", "w") as f:
            f.write(prod_env)
        print("✓ Production environment file created (.env.production)")
        print("⚠️  Please update the database URL and other settings!")
        return True
    except Exception as e:
        print(f"❌ Failed to create production environment file: {e}")
        return False

def build_frontend():
    """Build the React frontend for production"""
    print("🏗️ Building React frontend for production...")
    
    if not run_command("npm run build", "Building React application"):
        return False
    
    print("✓ Frontend build completed")
    return True

def install_production_dependencies():
    """Install production Python dependencies"""
    print("📦 Installing production dependencies...")
    
    # Install with production flag
    if not run_command("pip install -r requirements.txt --no-dev", "Installing Python dependencies"):
        # Fallback to regular install if --no-dev flag is not supported
        if not run_command("pip install -r requirements.txt", "Installing Python dependencies (fallback)"):
            return False
    
    return True

def create_systemd_service():
    """Create systemd service file for production deployment"""
    print("🔧 Creating systemd service file...")
    
    current_dir = os.getcwd()
    service_content = f"""[Unit]
Description=171 Bureau De Change API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory={current_dir}
Environment=PATH={current_dir}/venv/bin
ExecStart={current_dir}/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    try:
        with open("171-bdc.service", "w") as f:
            f.write(service_content)
        print("✓ Systemd service file created (171-bdc.service)")
        print("  To install: sudo cp 171-bdc.service /etc/systemd/system/")
        print("  To enable: sudo systemctl enable 171-bdc")
        print("  To start: sudo systemctl start 171-bdc")
        return True
    except Exception as e:
        print(f"❌ Failed to create systemd service file: {e}")
        return False

def create_nginx_config():
    """Create nginx configuration for reverse proxy"""
    print("🌐 Creating nginx configuration...")
    
    nginx_config = """server {
    listen 80;
    server_name yourdomain.com;  # Change this to your domain
    
    # Serve React build files
    location / {
        root /path/to/your/app/build;  # Change this path
        try_files $uri $uri/ /index.html;
    }
    
    # Proxy API requests to FastAPI
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Optional: Serve static files directly
    location /static {
        alias /path/to/your/app/build/static;  # Change this path
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
"""
    
    try:
        with open("nginx-171-bdc.conf", "w") as f:
            f.write(nginx_config)
        print("✓ Nginx configuration created (nginx-171-bdc.conf)")
        print("  To install: sudo cp nginx-171-bdc.conf /etc/nginx/sites-available/")
        print("  To enable: sudo ln -s /etc/nginx/sites-available/nginx-171-bdc.conf /etc/nginx/sites-enabled/")
        print("  To reload: sudo nginx -t && sudo systemctl reload nginx")
        return True
    except Exception as e:
        print(f"❌ Failed to create nginx configuration: {e}")
        return False

def create_docker_files():
    """Create Docker configuration files"""
    print("🐳 Creating Docker configuration...")
    
    # Dockerfile
    dockerfile_content = """FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app/ ./app/
COPY scripts/ ./scripts/

# Create non-root user
RUN useradd --create-home --shell /bin/bash app \\
    && chown -R app:app /app
USER app

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
"""
    
    # docker-compose.yml
    docker_compose_content = """version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/bureau_de_change
      - SECRET_KEY=your-secret-key-here
    depends_on:
      - db
    volumes:
      - ./app:/app/app
    
  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=bureau_de_change
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
"""
    
    try:
        with open("Dockerfile", "w") as f:
            f.write(dockerfile_content)
        
        with open("docker-compose.yml", "w") as f:
            f.write(docker_compose_content)
        
        print("✓ Docker configuration created")
        print("  To build: docker-compose build")
        print("  To run: docker-compose up -d")
        return True
    except Exception as e:
        print(f"❌ Failed to create Docker configuration: {e}")
        return False

def main():
    """Main production setup function"""
    print("🚀 Setting up 171 Bureau De Change Production Environment")
    print("=" * 60)
    
    # Setup steps
    steps = [
        ("Production environment configuration", create_production_env),
        ("Production dependencies", install_production_dependencies),
        ("Frontend build", build_frontend),
        ("Systemd service", create_systemd_service),
        ("Nginx configuration", create_nginx_config),
        ("Docker configuration", create_docker_files),
    ]
    
    failed_steps = []
    
    for step_name, step_function in steps:
        print(f"\n📋 Setting up {step_name}...")
        if not step_function():
            failed_steps.append(step_name)
    
    print("\n" + "=" * 60)
    
    if failed_steps:
        print("❌ Production setup completed with errors:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\nPlease fix the errors and run the setup again.")
    else:
        print("✅ Production setup completed successfully!")
        print("\n🚀 Next steps for deployment:")
        print("1. Update .env.production with your actual database URL and settings")
        print("2. Set up PostgreSQL database")
        print("3. Run database initialization: python scripts/init_db.py")
        print("4. Configure nginx or use Docker deployment")
        print("5. Set up SSL certificate (recommended: Let's Encrypt)")
        print("6. Configure firewall and security settings")

if __name__ == "__main__":
    main()
