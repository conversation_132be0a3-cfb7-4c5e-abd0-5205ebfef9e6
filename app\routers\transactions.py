from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from decimal import Decimal

from models.database import get_db
from models.transaction import Transaction
from models.user import User
from schemas.transaction import Transaction as TransactionSchema, TransactionCreate, TransactionUpdate
from services.transaction_processor import TransactionProcessor
from utils.dependencies import get_current_user

router = APIRouter()

@router.get("/", response_model=List[TransactionSchema])
async def get_transactions(
    skip: int = 0, 
    limit: int = 100,
    partner_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    query = db.query(Transaction)
    if partner_id:
        query = query.filter(Transaction.partner_id == partner_id)
    
    transactions = query.offset(skip).limit(limit).all()
    return transactions

@router.post("/", response_model=TransactionSchema)
async def create_transaction(
    transaction: TransactionCreate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    return TransactionProcessor.create_transaction(db, transaction, current_user.id)

@router.get("/{transaction_id}", response_model=TransactionSchema)
async def get_transaction(
    transaction_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    transaction = db.query(Transaction).filter(Transaction.id == transaction_id).first()
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")
    return transaction

@router.put("/{transaction_id}", response_model=TransactionSchema)
async def update_transaction(
    transaction_id: int, 
    transaction_update: TransactionUpdate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    transaction = db.query(Transaction).filter(Transaction.id == transaction_id).first()
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")
    
    update_data = transaction_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(transaction, field, value)
    
    db.commit()
    db.refresh(transaction)
    return transaction

@router.post("/balance")
async def process_balance(
    partner_id: int,
    usd_amount: float = 0,
    ngn_amount: float = 0,
    current_rate: float = 1600,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    return TransactionProcessor.process_mixed_return(
        db, 
        partner_id, 
        Decimal(str(usd_amount)), 
        Decimal(str(ngn_amount)), 
        Decimal(str(current_rate)),
        current_user.id
    )
