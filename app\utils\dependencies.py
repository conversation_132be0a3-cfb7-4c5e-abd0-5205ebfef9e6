import os
from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON><PERSON><PERSON><PERSON><PERSON>Bearer
from jose import J<PERSON><PERSON>rror, jwt
from sqlalchemy.orm import Session

from models.database import get_db
from models.user import User
from schemas.user import TokenData

# Get secret key from environment variable
SECRET_KEY = os.getenv("SECRET_KEY", "dev-secret-key-change-in-production-171bdc2024")
ALGORITHM = "HS256"

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token")

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception

    user = db.query(User).filter(User.username == token_data.username).first()
    if user is None:
        raise credentials_exception
    return user
