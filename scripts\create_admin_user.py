#!/usr/bin/env python3
"""
Create admin user in the database
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from models.database import get_db
from models.user import User
from utils.security import get_password_hash

def create_admin_user():
    """Create admin user"""
    print("👤 Creating admin user...")
    
    try:
        # Get database session
        db = next(get_db())
        
        # Check if admin user already exists
        existing_user = db.query(User).filter(User.username == "admin").first()
        if existing_user:
            print("ℹ️  Admin user already exists")
            db.close()
            return True
        
        # Create admin user
        hashed_password = get_password_hash("admin123")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password_hash=hashed_password,
            role="admin"
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print(f"✅ Admin user created successfully!")
        print(f"   ID: {admin_user.id}")
        print(f"   Username: {admin_user.username}")
        print(f"   Email: {admin_user.email}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        return False

if __name__ == "__main__":
    create_admin_user()
