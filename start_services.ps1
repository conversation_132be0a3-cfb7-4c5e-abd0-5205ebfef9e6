# 171 Bureau De Change - Service Startup Script (PowerShell)
# This script starts all required services for the application

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "171 Bureau De Change - Starting Services" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Check if we're in the correct directory
if (-not (Test-Path "app\main.py")) {
    Write-Host "ERROR: Please run this script from the project root directory" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Red
    Write-Host "Expected files: app\main.py, package.json" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

try {
    # Step 1: Activate conda environment
    Write-Host "[1/6] Activating conda environment..." -ForegroundColor Yellow
    & conda activate bdc
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to activate conda environment 'bdc'" -ForegroundColor Red
        Write-Host "Please ensure conda environment 'bdc' exists" -ForegroundColor Red
        Write-Host "Run: conda create --name bdc python=3.12" -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit 1
    }

    # Step 2: Activate Python virtual environment
    Write-Host "[2/6] Activating Python virtual environment..." -ForegroundColor Yellow
    if (Test-Path "venv\Scripts\Activate.ps1") {
        & .\venv\Scripts\Activate.ps1
    } elseif (Test-Path "venv\Scripts\activate.bat") {
        & .\venv\Scripts\activate.bat
    } else {
        Write-Host "WARNING: Python virtual environment not found" -ForegroundColor Yellow
        Write-Host "Creating virtual environment..." -ForegroundColor Yellow
        & python -m venv venv
        & .\venv\Scripts\Activate.ps1
        & pip install -r requirements.txt
    }

    # Step 3: Check database
    Write-Host "[3/6] Checking database..." -ForegroundColor Yellow
    if (-not (Test-Path "bureau_de_change.db")) {
        Write-Host "Initializing database..." -ForegroundColor Yellow
        $env:PYTHONPATH = Get-Location
        & python scripts\init_db.py
        if ($LASTEXITCODE -ne 0) {
            Write-Host "ERROR: Database initialization failed" -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit 1
        }
    }

    # Step 4: Check Node.js dependencies
    Write-Host "[4/6] Checking Node.js dependencies..." -ForegroundColor Yellow
    if (-not (Test-Path "node_modules")) {
        Write-Host "Installing Node.js dependencies..." -ForegroundColor Yellow
        & npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Trying with increased timeout..." -ForegroundColor Yellow
            & npm install --fetch-timeout=300000 --fetch-retry-mintimeout=20000
            if ($LASTEXITCODE -ne 0) {
                Write-Host "ERROR: npm install failed" -ForegroundColor Red
                Read-Host "Press Enter to exit"
                exit 1
            }
        }
    }

    Write-Host "[5/6] Starting services..." -ForegroundColor Yellow

    # Start backend server
    Write-Host "Starting FastAPI backend server..." -ForegroundColor Green
    $backendScript = @"
conda activate bdc
.\venv\Scripts\Activate.ps1
`$env:PYTHONPATH = Get-Location
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
"@
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $backendScript -WindowStyle Normal

    # Wait for backend to start
    Start-Sleep -Seconds 3

    # Start frontend server
    Write-Host "Starting React frontend server..." -ForegroundColor Green
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm start" -WindowStyle Normal

    Write-Host "[6/6] Services started!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Services Status:" -ForegroundColor Cyan
    Write-Host "✓ Backend:  http://localhost:8000" -ForegroundColor Green
    Write-Host "✓ Frontend: http://localhost:3000" -ForegroundColor Green
    Write-Host "✓ API Docs: http://localhost:8000/docs" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Default login credentials:" -ForegroundColor Yellow
    Write-Host "Username: admin" -ForegroundColor White
    Write-Host "Password: admin123" -ForegroundColor White
    Write-Host ""

    # Wait a bit more for services to fully start
    Write-Host "Waiting for services to fully start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5

    # Open application in browser
    Write-Host "Opening application in browser..." -ForegroundColor Green
    Start-Process "http://localhost:3000"

    Write-Host ""
    Write-Host "Services are running in separate windows." -ForegroundColor Green
    Write-Host "Close those windows to stop the services." -ForegroundColor Yellow
    Read-Host "Press Enter to exit this script"

} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
