#!/usr/bin/env python3
"""
Fix the root database schema by adding the missing channel column
"""

import sqlite3
import os
import shutil
from datetime import datetime

def fix_root_database():
    """Fix the root database schema"""
    print("🔧 Fixing root database schema...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'bureau_de_change.db')
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Create backup
    print(f"📦 Creating backup: {backup_path}")
    shutil.copy2(db_path, backup_path)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if channel column already exists
        cursor.execute("PRAGMA table_info(transactions)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'channel' in columns:
            print("ℹ️  Channel column already exists!")
            return True
        
        print("➕ Adding channel column...")
        
        # Add the channel column with default value
        cursor.execute("""
            ALTER TABLE transactions 
            ADD COLUMN channel TEXT DEFAULT 'cash'
        """)
        
        # Update existing transactions to have 'cash' channel
        cursor.execute("""
            UPDATE transactions 
            SET channel = 'cash' 
            WHERE channel IS NULL OR channel = ''
        """)
        
        # Commit changes
        conn.commit()
        
        # Verify the fix
        cursor.execute("PRAGMA table_info(transactions)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'channel' in columns:
            print("✅ Channel column added successfully!")
            
            # Show updated data
            cursor.execute("SELECT id, transaction_type, amount, currency, channel FROM transactions LIMIT 3")
            transactions = cursor.fetchall()
            
            print("📊 Updated transactions:")
            for trans in transactions:
                print(f"   ID: {trans[0]}, Type: {trans[1]}, Amount: {trans[2]} {trans[3]}, Channel: {trans[4]}")
            
            return True
        else:
            print("❌ Failed to add channel column!")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        # Restore backup on error
        print("🔄 Restoring backup...")
        shutil.copy2(backup_path, db_path)
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    if fix_root_database():
        print("\n🎉 Root database schema fixed successfully!")
        print("💡 The server should now work properly.")
        print("🔄 Restart the server to ensure it picks up the changes.")
    else:
        print("\n❌ Failed to fix root database schema.")
        print("🔧 Check the error messages above for details.")
