#!/usr/bin/env python3
"""
Database initialization script for 171 Bureau De Change
Creates tables and default admin user
"""

import sys
import os
from pathlib import Path

# Fix Unicode encoding issues on Windows
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))

from sqlalchemy.orm import Session
from models.database import engine, SessionLocal
from models.user import User, UserRole
from models.partner import Partner
from models.transaction import Transaction
from utils.security import get_password_hash

def create_tables():
    """Create all database tables"""
    print("Creating database tables...")
    
    # Import all models to ensure they're registered
    from models import user, partner, transaction
    
    # Create all tables
    User.metadata.create_all(bind=engine)
    Partner.metadata.create_all(bind=engine)
    Transaction.metadata.create_all(bind=engine)
    
    print("✓ Database tables created successfully")

def create_default_admin(db: Session):
    """Create default admin user if it doesn't exist"""
    print("Creating default admin user...")
    
    # Check if admin user already exists
    admin_user = db.query(User).filter(User.username == "admin").first()
    if admin_user:
        print("✓ Admin user already exists")
        return
    
    # Create admin user
    admin_user = User(
        username="admin",
        email="<EMAIL>",
        password_hash=get_password_hash("admin123"),
        role=UserRole.ADMIN,
        is_active=True
    )
    
    db.add(admin_user)
    db.commit()
    db.refresh(admin_user)
    
    print("✓ Default admin user created")
    print("  Username: admin")
    print("  Password: admin123")
    print("  ⚠️  Please change the default password after first login!")

def create_sample_data(db: Session):
    """Create sample data for development"""
    print("Creating sample data...")
    
    # Check if sample data already exists
    if db.query(Partner).first():
        print("✓ Sample data already exists")
        return
    
    # Create sample partner
    sample_partner = Partner(
        name="Sample Trading Partner",
        phone="+234-************",
        email="<EMAIL>",
        address="Lagos, Nigeria",
        status="active"
    )
    
    db.add(sample_partner)
    db.commit()
    db.refresh(sample_partner)
    
    print("✓ Sample data created")

def main():
    """Main initialization function"""
    print("🚀 Initializing 171 Bureau De Change Database")
    print("=" * 50)
    
    try:
        # Create tables
        create_tables()
        
        # Create database session
        db = SessionLocal()
        
        try:
            # Create default admin user
            create_default_admin(db)
            
            # Create sample data (optional)
            create_sample_data(db)
            
            print("\n✅ Database initialization completed successfully!")
            print("\nNext steps:")
            print("1. Start the FastAPI server: uvicorn app.main:app --reload")
            print("2. Start the React frontend: npm start")
            print("3. Login with admin/admin123 and change the password")
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"\n❌ Database initialization failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
