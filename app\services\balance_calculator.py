from sqlalchemy.orm import Session
from models.transaction import Transaction, TransactionType
from typing import List, Dict, Any
from decimal import Decimal

class BalanceCalculator:
    @staticmethod
    def calculate_partner_balance(db: Session, partner_id: int) -> Dict[str, Any]:
        """Calculate partner's current balance and transaction summary"""
        transactions = db.query(Transaction).filter(
            Transaction.partner_id == partner_id,
            Transaction.status == "completed"
        ).all()
        
        total_given = sum(
            t.usd_equivalent for t in transactions 
            if t.transaction_type == TransactionType.FUNDS_GIVEN
        )
        
        total_returned = sum(
            t.usd_equivalent for t in transactions 
            if t.transaction_type in [TransactionType.FUNDS_RETURNED, TransactionType.PARTIAL_RETURN]
        )
        
        current_balance = total_given - total_returned

        return {
            "partner_id": partner_id,
            "total_given": float(total_given),
            "total_returned": float(total_returned),
            "outstanding_balance": float(current_balance),
            "current_balance": float(current_balance),
            "transaction_count": len(transactions)
        }
    
    @staticmethod
    def calculate_chronological_balance(db: Session, partner_id: int, return_amount: Decimal) -> List[Dict]:
        """Calculate FIFO balance allocation"""
        given_transactions = db.query(Transaction).filter(
            Transaction.partner_id == partner_id,
            Transaction.transaction_type == TransactionType.FUNDS_GIVEN,
            Transaction.status == "completed"
        ).order_by(Transaction.created_at).all()
        
        remaining_return = return_amount
        balance_details = []
        
        for transaction in given_transactions:
            if remaining_return <= 0:
                break
                
            # Calculate already returned amount for this transaction
            returned_from_this = db.query(Transaction).filter(
                Transaction.partner_id == partner_id,
                Transaction.transaction_type.in_([TransactionType.FUNDS_RETURNED, TransactionType.PARTIAL_RETURN]),
                Transaction.created_at > transaction.created_at,
                Transaction.status == "completed"
            ).first()
            
            already_returned = returned_from_this.usd_equivalent if returned_from_this else Decimal(0)
            available_amount = transaction.usd_equivalent - already_returned
            
            if available_amount > 0:
                return_from_this = min(remaining_return, available_amount)
                
                balance_details.append({
                    "transaction_id": transaction.id,
                    "original_amount": float(transaction.usd_equivalent),
                    "original_rate": float(transaction.exchange_rate) if transaction.exchange_rate else None,
                    "returned_amount": float(return_from_this),
                    "remaining_amount": float(available_amount - return_from_this),
                    "transaction_date": transaction.created_at
                })
                
                remaining_return -= return_from_this
        
        return balance_details

    @staticmethod
    def calculate_multi_currency_outstanding(db: Session) -> Dict[str, Any]:
        """Calculate total outstanding balances in all currencies"""
        # Get all outstanding transactions (funds given but not returned)
        given_transactions = db.query(Transaction).filter(
            Transaction.transaction_type == TransactionType.FUNDS_GIVEN,
            Transaction.status == "completed"
        ).all()

        returned_transactions = db.query(Transaction).filter(
            Transaction.transaction_type.in_([TransactionType.FUNDS_RETURNED, TransactionType.PARTIAL_RETURN]),
            Transaction.status == "completed"
        ).all()

        # Calculate total given by currency
        given_by_currency = {}
        for transaction in given_transactions:
            currency = transaction.currency
            if currency not in given_by_currency:
                given_by_currency[currency] = {
                    'amount': Decimal(0),
                    'usd_equivalent': Decimal(0),
                    'transactions': []
                }
            given_by_currency[currency]['amount'] += transaction.amount
            given_by_currency[currency]['usd_equivalent'] += transaction.usd_equivalent
            given_by_currency[currency]['transactions'].append(transaction)

        # Calculate total returned by currency
        returned_by_currency = {}
        for transaction in returned_transactions:
            currency = transaction.currency
            if currency not in returned_by_currency:
                returned_by_currency[currency] = {
                    'amount': Decimal(0),
                    'usd_equivalent': Decimal(0)
                }
            returned_by_currency[currency]['amount'] += transaction.amount
            returned_by_currency[currency]['usd_equivalent'] += transaction.usd_equivalent

        # Calculate outstanding balances
        outstanding_balances = {}
        all_currencies = set(given_by_currency.keys()) | set(returned_by_currency.keys())

        for currency in all_currencies:
            given_amount = given_by_currency.get(currency, {}).get('amount', Decimal(0))
            returned_amount = returned_by_currency.get(currency, {}).get('amount', Decimal(0))
            outstanding_amount = given_amount - returned_amount

            given_usd = given_by_currency.get(currency, {}).get('usd_equivalent', Decimal(0))
            returned_usd = returned_by_currency.get(currency, {}).get('usd_equivalent', Decimal(0))
            outstanding_usd = given_usd - returned_usd

            outstanding_balances[currency] = {
                'amount': float(outstanding_amount),
                'usd_equivalent': float(outstanding_usd)
            }

        # Calculate weighted average rates for outstanding amounts
        for currency in outstanding_balances:
            if currency != 'USD' and outstanding_balances[currency]['amount'] > 0:
                # Calculate weighted average rate from outstanding transactions
                total_amount = Decimal(0)
                total_usd = Decimal(0)

                for transaction in given_by_currency.get(currency, {}).get('transactions', []):
                    # Check how much of this transaction is still outstanding
                    partner_returned = sum(
                        t.usd_equivalent for t in returned_transactions
                        if t.partner_id == transaction.partner_id and t.created_at > transaction.created_at
                    )

                    if transaction.usd_equivalent > partner_returned:
                        outstanding_from_this = transaction.usd_equivalent - partner_returned
                        proportion = outstanding_from_this / transaction.usd_equivalent

                        total_amount += transaction.amount * proportion
                        total_usd += outstanding_from_this

                if total_usd > 0:
                    outstanding_balances[currency]['average_rate'] = float(total_amount / total_usd)

        return outstanding_balances
