#!/usr/bin/env python3
"""
Test transaction queries after fixing enum values
"""

import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from models.database import get_db
from models.transaction import Transaction
from models.partner import Partner
from services.balance_calculator import BalanceCalculator

def test_queries():
    """Test various database queries"""
    print("🧪 Testing database queries after enum fix...")
    
    try:
        # Get database session
        db = next(get_db())
        
        # Test basic transaction query
        print("\n📤 Testing basic transaction query...")
        transactions = db.query(Transaction).limit(3).all()
        print(f"   ✅ Found {len(transactions)} transactions")
        
        if transactions:
            trans = transactions[0]
            print(f"   Sample: ID={trans.id}, Type={trans.transaction_type}, Status={trans.status}, Channel={trans.channel}")
        
        # Test partner query
        print("\n📤 Testing partner query...")
        partners = db.query(Partner).limit(3).all()
        print(f"   ✅ Found {len(partners)} partners")
        
        if partners:
            partner = partners[0]
            print(f"   Sample: ID={partner.id}, Name={partner.name}")
            
            # Test balance calculation for this partner
            print(f"\n📤 Testing balance calculation for partner {partner.id}...")
            try:
                balance_info = BalanceCalculator.calculate_partner_balance(db, partner.id)
                print(f"   ✅ Balance calculated: {balance_info}")
            except Exception as e:
                print(f"   ❌ Balance calculation failed: {e}")
        
        # Test multi-currency balance calculation
        print("\n📤 Testing multi-currency balance calculation...")
        try:
            multi_balances = BalanceCalculator.calculate_multi_currency_outstanding(db)
            print(f"   ✅ Multi-currency balances: {multi_balances}")
        except Exception as e:
            print(f"   ❌ Multi-currency calculation failed: {e}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_queries()
