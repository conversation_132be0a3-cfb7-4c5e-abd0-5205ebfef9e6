#!/usr/bin/env python3
"""
Test endpoints on port 8001
"""

import requests

def test_endpoints():
    """Test basic endpoints"""
    print("🧪 Testing endpoints on port 8001...")
    
    try:
        # Test health endpoint
        print("📤 Testing health endpoint...")
        response = requests.get("http://localhost:8001/health")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
        
        # Test auth
        print("\n📤 Testing auth...")
        auth_response = requests.post("http://localhost:8001/api/auth/token", data={
            "username": "admin",
            "password": "admin123"
        })
        print(f"   Status: {auth_response.status_code}")
        
        if auth_response.status_code == 200:
            token = auth_response.json()["access_token"]
            print("   ✅ Auth successful")
            
            # Test partners endpoint
            print("\n📤 Testing partners endpoint...")
            headers = {"Authorization": f"Bearer {token}"}
            partners_response = requests.get("http://localhost:8001/api/partners/", headers=headers)
            print(f"   Status: {partners_response.status_code}")
            
            if partners_response.status_code == 200:
                partners = partners_response.json()
                print(f"   ✅ Partners: {len(partners)} found")
                if partners:
                    print(f"   Sample: {partners[0].get('name')}")
            else:
                print(f"   ❌ Error: {partners_response.text}")
        else:
            print(f"   ❌ Auth failed: {auth_response.text}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_endpoints()
