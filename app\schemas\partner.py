from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
from models.partner import PartnerStatus

class PartnerBase(BaseModel):
    name: str
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None

class PartnerCreate(PartnerBase):
    pass

class PartnerUpdate(BaseModel):
    name: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    status: Optional[PartnerStatus] = None

class Partner(PartnerBase):
    id: int
    status: PartnerStatus
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class PartnerWithBalance(Partner):
    current_balance: float
    total_given: float
    total_returned: float
    transaction_count: int
