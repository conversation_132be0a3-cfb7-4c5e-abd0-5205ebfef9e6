#!/usr/bin/env python3
"""
<PERSON><PERSON>t to fix database schema by adding missing columns
"""

import sqlite3
import os
import sys

# Add the app directory to the path so we can import models
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from models.database import engine, Base
from models.transaction import Transaction, TransactionChannel
from models.partner import Partner
from models.user import User

def check_database_schema():
    """Check current database schema"""
    print("🔍 Checking current database schema...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'bureau_de_change.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check transactions table schema
        cursor.execute("PRAGMA table_info(transactions)")
        columns = cursor.fetchall()
        
        print(f"📋 Current transactions table columns:")
        column_names = []
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
            column_names.append(col[1])
        
        # Check if channel column exists
        has_channel = 'channel' in column_names
        print(f"\n✅ Channel column exists: {has_channel}")
        
        return has_channel
        
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
        return False
    finally:
        conn.close()

def add_missing_columns():
    """Add missing columns to the database"""
    print("\n🔧 Adding missing columns...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'bureau_de_change.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Add channel column with default value
        print("📝 Adding channel column...")
        cursor.execute("""
            ALTER TABLE transactions 
            ADD COLUMN channel TEXT DEFAULT 'cash'
        """)
        
        # Update existing records to have a valid channel value
        print("📝 Updating existing records with default channel value...")
        cursor.execute("""
            UPDATE transactions 
            SET channel = 'cash' 
            WHERE channel IS NULL OR channel = ''
        """)
        
        conn.commit()
        print("✅ Successfully added channel column")
        
        return True
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("ℹ️  Channel column already exists")
            return True
        else:
            print(f"❌ Error adding column: {e}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

def recreate_database():
    """Recreate the entire database with correct schema"""
    print("\n🔄 Recreating database with correct schema...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'bureau_de_change.db')
    
    # Backup existing data
    backup_data = {}
    if os.path.exists(db_path):
        print("💾 Backing up existing data...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # Backup users
            cursor.execute("SELECT * FROM users")
            backup_data['users'] = cursor.fetchall()
            cursor.execute("PRAGMA table_info(users)")
            backup_data['users_schema'] = [col[1] for col in cursor.fetchall()]
            
            # Backup partners
            cursor.execute("SELECT * FROM partners")
            backup_data['partners'] = cursor.fetchall()
            cursor.execute("PRAGMA table_info(partners)")
            backup_data['partners_schema'] = [col[1] for col in cursor.fetchall()]
            
            # Backup transactions (without channel column)
            cursor.execute("PRAGMA table_info(transactions)")
            trans_columns = [col[1] for col in cursor.fetchall()]
            backup_data['transactions_schema'] = trans_columns
            
            # Select only existing columns
            existing_cols = [col for col in trans_columns if col != 'channel']
            cursor.execute(f"SELECT {', '.join(existing_cols)} FROM transactions")
            backup_data['transactions'] = cursor.fetchall()
            backup_data['transactions_columns'] = existing_cols
            
            print(f"   📊 Backed up {len(backup_data['users'])} users")
            print(f"   📊 Backed up {len(backup_data['partners'])} partners") 
            print(f"   📊 Backed up {len(backup_data['transactions'])} transactions")
            
        except Exception as e:
            print(f"⚠️  Warning: Could not backup data: {e}")
        finally:
            conn.close()
    
    # Remove old database
    if os.path.exists(db_path):
        os.remove(db_path)
        print("🗑️  Removed old database")
    
    # Create new database with correct schema
    print("🏗️  Creating new database...")
    Base.metadata.create_all(bind=engine)
    print("✅ Created new database with correct schema")
    
    # Restore data
    if backup_data:
        print("📥 Restoring data...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # Restore users
            if backup_data.get('users'):
                placeholders = ', '.join(['?' for _ in backup_data['users_schema']])
                cursor.executemany(
                    f"INSERT INTO users ({', '.join(backup_data['users_schema'])}) VALUES ({placeholders})",
                    backup_data['users']
                )
                print(f"   ✅ Restored {len(backup_data['users'])} users")
            
            # Restore partners
            if backup_data.get('partners'):
                placeholders = ', '.join(['?' for _ in backup_data['partners_schema']])
                cursor.executemany(
                    f"INSERT INTO partners ({', '.join(backup_data['partners_schema'])}) VALUES ({placeholders})",
                    backup_data['partners']
                )
                print(f"   ✅ Restored {len(backup_data['partners'])} partners")
            
            # Restore transactions with default channel
            if backup_data.get('transactions'):
                # Add channel column with default value
                columns_with_channel = backup_data['transactions_columns'] + ['channel']
                placeholders = ', '.join(['?' for _ in columns_with_channel])
                
                # Add default 'cash' value to each transaction
                transactions_with_channel = [
                    list(trans) + ['cash'] for trans in backup_data['transactions']
                ]
                
                cursor.executemany(
                    f"INSERT INTO transactions ({', '.join(columns_with_channel)}) VALUES ({placeholders})",
                    transactions_with_channel
                )
                print(f"   ✅ Restored {len(backup_data['transactions'])} transactions with default channel")
            
            conn.commit()
            print("✅ Data restoration complete")
            
        except Exception as e:
            print(f"❌ Error restoring data: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    return True

def main():
    """Main function"""
    print("🚀 Database Schema Fix Tool")
    print("=" * 50)
    
    # Check current schema
    has_channel = check_database_schema()
    
    if has_channel:
        print("\n✅ Database schema is correct!")
        return
    
    print("\n❌ Database schema needs to be fixed")
    print("\n🔧 Attempting to add missing column...")
    
    # Try to add missing column first
    if add_missing_columns():
        print("\n✅ Successfully fixed database schema!")
        
        # Verify the fix
        print("\n🔍 Verifying fix...")
        if check_database_schema():
            print("✅ Database schema is now correct!")
        else:
            print("❌ Schema fix verification failed")
    else:
        print("\n⚠️  Could not add column. Recreating database...")
        if recreate_database():
            print("\n✅ Successfully recreated database!")
        else:
            print("\n❌ Failed to recreate database")

if __name__ == "__main__":
    main()
