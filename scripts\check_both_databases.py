#!/usr/bin/env python3
"""
Check both database files to see which one has data
"""

import sqlite3
import os

def check_database(db_path, name):
    """Check a specific database file"""
    print(f"\n🔍 Checking {name}: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # List all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 Tables: {[t[0] for t in tables]}")
        
        # Check each table's content
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   {table_name}: {count} rows")
            
            # Show sample data for non-empty tables
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
                sample = cursor.fetchone()
                print(f"     Sample: {sample}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

def main():
    """Check both database files"""
    print("🚀 Checking Database Files")
    print("=" * 50)
    
    # Check root directory database
    root_db = os.path.join(os.path.dirname(__file__), '..', 'bureau_de_change.db')
    check_database(root_db, "Root Database")
    
    # Check app directory database
    app_db = os.path.join(os.path.dirname(__file__), '..', 'app', 'bureau_de_change.db')
    check_database(app_db, "App Database")
    
    print("\n" + "=" * 50)
    print("💡 The server should be using the ROOT database based on the configuration:")
    print("   DATABASE_URL = sqlite:///../bureau_de_change.db")
    print("💡 If the app database has data but root doesn't, we need to copy it over.")

if __name__ == "__main__":
    main()
