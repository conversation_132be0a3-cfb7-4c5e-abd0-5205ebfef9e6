# 171 Bureau De Change - Startup Guide

## Quick Start

### Automated Startup (Recommended)

1. **Windows Batch Script:**
   ```bash
   start_services.bat
   ```

2. **PowerShell Script:**
   ```powershell
   .\start_services.ps1
   ```

Both scripts will automatically:
- Activate the conda environment
- Set up Python virtual environment
- Initialize the database
- Install dependencies
- Start both backend and frontend services
- Open the application in your browser

## Manual Startup

If you prefer to start services manually or troubleshoot issues:

### Prerequisites

1. **Conda Environment:**
   ```bash
   conda create --name bdc python=3.12
   conda activate bdc
   ```

2. **Python Virtual Environment:**
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   # or
   source venv/bin/activate  # Linux/Mac
   ```

3. **Install Python Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install Node.js Dependencies:**
   ```bash
   npm install
   ```

### Database Setup

1. **Initialize Database:**
   ```bash
   set PYTHONPATH=%CD%  # Windows
   # or
   export PYTHONPATH=$(pwd)  # Linux/Mac
   
   python scripts/init_db.py
   ```

### Starting Services

1. **Start Backend (Terminal 1):**
   ```bash
   conda activate bdc
   venv\Scripts\activate  # Windows
   set PYTHONPATH=%CD%    # Windows
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Start Frontend (Terminal 2):**
   ```bash
   npm start
   ```

## Service URLs

- **Frontend Application:** http://localhost:3000
- **Backend API:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs
- **Alternative API Docs:** http://localhost:8000/redoc

## Default Credentials

- **Username:** admin
- **Password:** admin123

## Troubleshooting

### Common Issues

1. **Unicode Encoding Errors:**
   - Ensure console encoding is set to UTF-8
   - Use the provided startup scripts which handle this automatically

2. **Module Import Errors:**
   - Ensure PYTHONPATH is set to project root
   - Run backend from project root directory, not from app/ subdirectory

3. **npm Install Failures:**
   - Try clearing npm cache: `npm cache clean --force`
   - Use increased timeout: `npm install --fetch-timeout=300000`
   - Check network connectivity and proxy settings

4. **Database Connection Issues:**
   - Ensure database file exists: `bureau_de_change.db`
   - Re-run database initialization: `python scripts/init_db.py`

5. **Port Already in Use:**
   - Backend (8000): `netstat -ano | findstr :8000`
   - Frontend (3000): `netstat -ano | findstr :3000`
   - Kill processes or use different ports

### Environment Variables

Create a `.env` file in the project root with:

```env
# Database
DATABASE_URL=sqlite:///./bureau_de_change.db

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
CORS_ORIGINS=["http://localhost:3000"]

# Development
DEBUG=True
```

### Logs and Debugging

1. **Backend Logs:**
   - Console output from uvicorn
   - Check for import errors and database connection issues

2. **Frontend Logs:**
   - Browser console (F12)
   - Network tab for API call issues

3. **Database Logs:**
   - SQLite database file: `bureau_de_change.db`
   - Use SQLite browser to inspect data

## Development Workflow

1. **Making Changes:**
   - Backend: FastAPI auto-reloads on file changes
   - Frontend: React auto-reloads on file changes

2. **Database Changes:**
   - Modify models in `app/models/`
   - Update database initialization in `scripts/init_db.py`
   - Re-run database initialization

3. **Adding Dependencies:**
   - Python: Add to `requirements.txt` and run `pip install -r requirements.txt`
   - Node.js: Use `npm install package-name` or `yarn add package-name`

## Project Structure

```
bdc_app/
├── app/                    # Backend FastAPI application
│   ├── main.py            # FastAPI app entry point
│   ├── models/            # Database models
│   ├── routers/           # API route handlers
│   ├── utils/             # Utility functions
│   └── database.py        # Database configuration
├── src/                   # Frontend React application
│   ├── components/        # React components
│   ├── pages/            # Page components
│   ├── services/         # API service functions
│   └── utils/            # Frontend utilities
├── scripts/              # Setup and utility scripts
├── public/               # Static frontend assets
├── requirements.txt      # Python dependencies
├── package.json          # Node.js dependencies
├── start_services.bat    # Windows startup script
├── start_services.ps1    # PowerShell startup script
└── STARTUP_GUIDE.md      # This file
```

## Support

If you encounter issues not covered in this guide:

1. Check the console output for specific error messages
2. Ensure all prerequisites are installed correctly
3. Try the manual startup process to isolate issues
4. Check network connectivity for npm install issues
5. Verify file permissions and directory structure
