import React, { useState, useEffect, useCallback } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { Plus, AlertCircle, LogOut } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';
import DashboardView from './DashboardView';
import PartnersView from './PartnersView';
import TransactionsView from './TransactionsView';
import NewTransactionModal from './modals/NewTransactionModal';
import NewPartnerModal from './modals/NewPartnerModal';
import BalancingModal from './modals/BalancingModal';
import PartnerDetailsModal from './modals/PartnerDetailsModal';

const Dashboard = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [partners, setPartners] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [dashboardStats, setDashboardStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Modal states
  const [selectedPartner, setSelectedPartner] = useState(null);
  const [showNewTransaction, setShowNewTransaction] = useState(false);
  const [showNewPartner, setShowNewPartner] = useState(false);
  const [showBalancing, setShowBalancing] = useState(false);

  // Get current view from URL
  const getCurrentView = useCallback(() => {
    const path = location.pathname.split('/')[2] || 'overview';
    return ['overview', 'partners', 'transactions'].includes(path) ? path : 'overview';
  }, [location.pathname]);

  const [activeView, setActiveView] = useState(getCurrentView());

  useEffect(() => {
    setActiveView(getCurrentView());
  }, [getCurrentView]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [partnersData, transactionsData, statsData] = await Promise.all([
        apiService.getPartners(),
        apiService.getTransactions(),
        apiService.getDashboardStats()
      ]);
      
      setPartners(partnersData);
      setTransactions(transactionsData);
      setDashboardStats(statsData);
    } catch (err) {
      setError('Failed to load data: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleViewChange = (view) => {
    setActiveView(view);
    navigate(`/dashboard/${view}`);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleNewTransaction = async (transactionData) => {
    try {
      console.log('Creating transaction with data:', transactionData);
      await apiService.createTransaction(transactionData);
      await loadData(); // Refresh data
      setShowNewTransaction(false);
    } catch (err) {
      console.error('Transaction creation error:', err);
      const errorMessage = err.message || err.toString() || 'Unknown error occurred';
      setError('Failed to create transaction: ' + errorMessage);
    }
  };

  const handleNewPartner = async (partnerData) => {
    try {
      await apiService.createPartner(partnerData);
      await loadData(); // Refresh data
      setShowNewPartner(false);
    } catch (err) {
      setError('Failed to create partner: ' + err.message);
    }
  };

  const handleBalancing = async (balanceData) => {
    try {
      await apiService.processBalance(balanceData);
      await loadData(); // Refresh data
      setShowBalancing(false);
    } catch (err) {
      setError('Failed to process balance: ' + err.message);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">171 Bureau De Change</h1>
              <p className="text-gray-600">Trading Account Management System</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {user?.username}</span>
              <button
                onClick={() => setShowNewTransaction(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>New Transaction</span>
              </button>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {[
              { key: 'overview', label: 'Dashboard' },
              { key: 'partners', label: 'Partners' },
              { key: 'transactions', label: 'Transactions' }
            ].map((view) => (
              <button
                key={view.key}
                onClick={() => handleViewChange(view.key)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeView === view.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {view.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Error Display */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
                <button
                  onClick={() => setError('')}
                  className="text-sm text-red-600 underline mt-1"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <Routes>
          <Route path="/" element={<DashboardView stats={dashboardStats} transactions={transactions} partners={partners} />} />
          <Route path="/overview" element={<DashboardView stats={dashboardStats} transactions={transactions} partners={partners} />} />
          <Route path="/partners" element={
            <PartnersView 
              partners={partners} 
              onNewPartner={() => setShowNewPartner(true)}
              onViewPartner={setSelectedPartner}
              onBalance={(partner) => {
                setShowBalancing(true);
                // Set partner for balancing
              }}
            />
          } />
          <Route path="/transactions" element={<TransactionsView transactions={transactions} partners={partners} />} />
        </Routes>
      </main>

      {/* Modals */}
      {showNewTransaction && (
        <NewTransactionModal
          partners={partners}
          onSubmit={handleNewTransaction}
          onClose={() => setShowNewTransaction(false)}
        />
      )}

      {showNewPartner && (
        <NewPartnerModal
          onSubmit={handleNewPartner}
          onClose={() => setShowNewPartner(false)}
        />
      )}

      {showBalancing && (
        <BalancingModal
          partners={partners}
          onSubmit={handleBalancing}
          onClose={() => setShowBalancing(false)}
        />
      )}

      {selectedPartner && (
        <PartnerDetailsModal
          partner={selectedPartner}
          transactions={transactions.filter(t => t.partner_id === selectedPartner.id)}
          onClose={() => setSelectedPartner(null)}
        />
      )}
    </div>
  );
};

export default Dashboard;
