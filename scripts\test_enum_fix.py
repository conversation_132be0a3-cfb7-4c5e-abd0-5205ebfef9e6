#!/usr/bin/env python3
"""
Test that the enum fix is working correctly
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))

def test_enum_imports():
    """Test that we can import all enum classes without errors"""
    print("🧪 Testing enum imports...")
    
    try:
        from models.transaction import TransactionType, TransactionStatus, TransactionChannel
        print("✅ Successfully imported enum classes")
        
        # Test enum values
        print(f"   TransactionType.FUNDS_GIVEN = '{TransactionType.FUNDS_GIVEN}'")
        print(f"   TransactionStatus.COMPLETED = '{TransactionStatus.COMPLETED}'")
        print(f"   TransactionChannel.CASH = '{TransactionChannel.CASH}'")
        
        return True
    except Exception as e:
        print(f"❌ Failed to import enum classes: {e}")
        return False

def test_schema_validation():
    """Test that schema validation works with both old and new enum values"""
    print("\n🧪 Testing schema validation...")
    
    try:
        from schemas.transaction import TransactionCreate
        from models.transaction import TransactionType, TransactionChannel
        from decimal import Decimal
        
        # Test with lowercase values (backward compatibility)
        test_data_old = {
            "partner_id": 1,
            "transaction_type": "funds_given",
            "amount": Decimal("1000"),
            "currency": "USD",
            "channel": "cash"
        }
        
        transaction_old = TransactionCreate(**test_data_old)
        print(f"✅ Old format works: type={transaction_old.transaction_type}, channel={transaction_old.channel}")
        
        # Test with uppercase values (new format)
        test_data_new = {
            "partner_id": 1,
            "transaction_type": "FUNDS_GIVEN",
            "amount": Decimal("1000"),
            "currency": "USD",
            "channel": "CASH"
        }
        
        transaction_new = TransactionCreate(**test_data_new)
        print(f"✅ New format works: type={transaction_new.transaction_type}, channel={transaction_new.channel}")
        
        # Test with enum objects
        test_data_enum = {
            "partner_id": 1,
            "transaction_type": TransactionType.FUNDS_GIVEN,
            "amount": Decimal("1000"),
            "currency": "USD",
            "channel": TransactionChannel.CASH
        }
        
        transaction_enum = TransactionCreate(**test_data_enum)
        print(f"✅ Enum format works: type={transaction_enum.transaction_type}, channel={transaction_enum.channel}")
        
        return True
    except Exception as e:
        print(f"❌ Schema validation failed: {e}")
        return False

def test_database_query():
    """Test that we can query the database without enum errors"""
    print("\n🧪 Testing database query...")
    
    try:
        from models.database import SessionLocal
        from models.transaction import Transaction
        
        db = SessionLocal()
        try:
            # Try to query transactions
            transactions = db.query(Transaction).limit(1).all()
            print(f"✅ Database query successful, found {len(transactions)} transactions")
            
            if transactions:
                trans = transactions[0]
                print(f"   Sample: type={trans.transaction_type}, status={trans.status}, channel={trans.channel}")
            
            return True
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Database query failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Enum Fix")
    print("=" * 40)
    
    tests = [
        test_enum_imports,
        test_schema_validation,
        test_database_query
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enum fix is working correctly.")
        print("\n✨ Summary:")
        print("1. ✅ Enum classes import successfully")
        print("2. ✅ Schema validation works with old and new formats")
        print("3. ✅ Database queries work without enum errors")
        print("\n💡 The server should now start without the 'cash' enum error.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
