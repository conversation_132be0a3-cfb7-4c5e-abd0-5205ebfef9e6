#!/usr/bin/env python3
"""
Check the schema of the root database
"""

import sqlite3
import os

def check_schema():
    """Check the schema of the root database"""
    print("🔍 Checking root database schema...")
    
    db_path = os.path.join(os.path.dirname(__file__), '..', 'bureau_de_change.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check transactions table schema
        print("\n📋 Transactions table schema:")
        cursor.execute("PRAGMA table_info(transactions)")
        columns = cursor.fetchall()
        
        for col in columns:
            print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - Default: {col[4]}")
        
        # Check if channel column exists
        column_names = [col[1] for col in columns]
        if 'channel' in column_names:
            print("✅ Channel column exists!")
        else:
            print("❌ Channel column is MISSING!")
        
        # Show sample transaction data
        print("\n📊 Sample transaction data:")
        cursor.execute("SELECT * FROM transactions LIMIT 1")
        transaction = cursor.fetchone()
        if transaction:
            print(f"   Transaction: {transaction}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_schema()
