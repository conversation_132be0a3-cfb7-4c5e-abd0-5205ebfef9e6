# 171 Bureau De Change Application

A comprehensive bureau de change management system built with FastAPI (backend) and React (frontend).

## 🚀 Quick Start

### Automated Setup (Recommended)

1. **Windows Batch Script:**
   ```bash
   start_services.bat
   ```

2. **PowerShell Script:**
   ```powershell
   .\start_services.ps1
   ```

3. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

4. **Default Login:**
   - Username: `admin`
   - Password: `admin123`

> **Note:** The automated scripts handle all setup including environment activation, dependency installation, database initialization, and service startup.

## 📋 Features

- **Partner Management**: Add, edit, and manage exchange partners
- **Transaction Processing**: Handle currency exchange transactions
- **Rate Management**: Real-time exchange rate updates
- **Reporting**: Generate detailed transaction and partner reports
- **User Authentication**: Secure login system with JWT tokens
- **Dashboard**: Overview of business metrics and recent activities

## 🛠 Technology Stack

### Backend
- **FastAPI**: Modern, fast web framework for building APIs
- **SQLAlchemy**: SQL toolkit and ORM
- **SQLite**: Lightweight database for development
- **JWT**: JSON Web Tokens for authentication
- **Uvicorn**: ASGI server for running the application

### Frontend
- **React**: JavaScript library for building user interfaces
- **Material-UI**: React components implementing Google's Material Design
- **Axios**: HTTP client for API requests
- **React Router**: Declarative routing for React

## 📁 Project Structure

```
bdc_app/
├── app/                    # Backend FastAPI application
│   ├── main.py            # FastAPI app entry point
│   ├── models/            # Database models
│   ├── routers/           # API route handlers
│   ├── utils/             # Utility functions
│   └── database.py        # Database configuration
├── src/                   # Frontend React application
│   ├── components/        # React components
│   ├── pages/            # Page components
│   ├── services/         # API service functions
│   └── utils/            # Frontend utilities
├── scripts/              # Setup and utility scripts
├── public/               # Static frontend assets
├── requirements.txt      # Python dependencies
├── package.json          # Node.js dependencies
├── start_services.bat    # Windows startup script
├── start_services.ps1    # PowerShell startup script
├── STARTUP_GUIDE.md      # Detailed startup guide
└── README.md            # This file
```

## 🔧 Manual Setup (Advanced Users)

### Prerequisites
- Python 3.12+
- Node.js 14+
- Conda (recommended)

### Step-by-Step Installation

1. **Create Conda Environment:**
   ```bash
   conda create --name bdc python=3.12
   conda activate bdc
   ```

2. **Setup Python Environment:**
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   pip install -r requirements.txt
   ```

3. **Setup Node.js Environment:**
   ```bash
   npm install
   ```

4. **Initialize Database:**
   ```bash
   set PYTHONPATH=%CD%  # Windows
   python scripts/init_db.py
   ```

5. **Start Services:**
   
   **Backend (Terminal 1):**
   ```bash
   conda activate bdc
   venv\Scripts\activate
   set PYTHONPATH=%CD%
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```
   
   **Frontend (Terminal 2):**
   ```bash
   npm start
   ```

## 🌐 API Endpoints

### Authentication
- `POST /auth/login` - User login
- `POST /auth/register` - User registration

### Partners
- `GET /partners/` - List all partners
- `POST /partners/` - Create new partner
- `GET /partners/{id}` - Get partner by ID
- `PUT /partners/{id}` - Update partner
- `DELETE /partners/{id}` - Delete partner

### Transactions
- `GET /transactions/` - List all transactions
- `POST /transactions/` - Create new transaction
- `GET /transactions/{id}` - Get transaction by ID
- `PUT /transactions/{id}` - Update transaction
- `DELETE /transactions/{id}` - Delete transaction

### Reports
- `GET /reports/transactions` - Transaction reports
- `GET /reports/partners` - Partner reports
- `GET /reports/summary` - Summary reports

## 🐛 Troubleshooting

### Common Issues

1. **Unicode Encoding Errors:**
   - Use the provided startup scripts which handle UTF-8 encoding automatically

2. **Module Import Errors:**
   - Ensure you're running from the project root directory
   - Set PYTHONPATH to project root: `set PYTHONPATH=%CD%`

3. **npm Install Failures:**
   - Clear npm cache: `npm cache clean --force`
   - Use increased timeout: `npm install --fetch-timeout=300000`

4. **Database Issues:**
   - Re-run database initialization: `python scripts/init_db.py`

5. **Port Conflicts:**
   - Check for processes using ports 3000 and 8000
   - Kill conflicting processes or use different ports

### Quick Fix Script

If you encounter issues, run:
```bash
fix_current_issues.bat
```

## 📚 Documentation

- **[Startup Guide](STARTUP_GUIDE.md)** - Comprehensive setup and troubleshooting guide
- **[API Documentation](http://localhost:8000/docs)** - Interactive API documentation (when server is running)

## 🧪 Development

### Running Tests
```bash
# Backend tests
pytest

# Frontend tests
npm test
```

### Environment Variables

Create a `.env` file in the root directory:

```env
DATABASE_URL=sqlite:///./bureau_de_change.db
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
CORS_ORIGINS=["http://localhost:3000"]
DEBUG=True
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🆘 Need Help?

1. Check the [Startup Guide](STARTUP_GUIDE.md) for detailed instructions
2. Use the automated startup scripts for easiest setup
3. Ensure all prerequisites are installed correctly
4. Check console output for specific error messages
