#!/usr/bin/env python3
"""
Test creation endpoints to ensure UI can create new data
"""

import requests
import json

def get_auth_token():
    """Get authentication token"""
    print("🔐 Getting authentication token...")
    
    response = requests.post("http://localhost:8000/api/auth/token", data={
        "username": "admin",
        "password": "admin123"
    })
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print(f"✅ Token obtained")
        return token
    else:
        print(f"❌ Auth failed: {response.status_code} - {response.text}")
        return None

def test_create_partner(token):
    """Test creating a new partner"""
    print("\n🧪 Testing partner creation...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    partner_data = {
        "name": "Test Partner",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "address": "123 Test Street"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/partners/",
            headers=headers,
            json=partner_data
        )
        
        print(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            partner = response.json()
            print(f"✅ Partner created successfully!")
            print(f"   ID: {partner.get('id')}")
            print(f"   Name: {partner.get('name')}")
            return partner.get('id')
        else:
            print(f"❌ Failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_create_transaction(token, partner_id):
    """Test creating a new transaction"""
    print("\n🧪 Testing transaction creation...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    transaction_data = {
        "partner_id": partner_id,
        "transaction_type": "funds_given",
        "amount": 1000.00,
        "currency": "USD",
        "exchange_rate": 1.0,
        "channel": "cash",
        "notes": "Test transaction"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/transactions/",
            headers=headers,
            json=transaction_data
        )
        
        print(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            transaction = response.json()
            print(f"✅ Transaction created successfully!")
            print(f"   ID: {transaction.get('id')}")
            print(f"   Amount: {transaction.get('amount')} {transaction.get('currency')}")
            print(f"   Channel: {transaction.get('channel')}")
            return transaction.get('id')
        else:
            print(f"❌ Failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_get_partners(token):
    """Test getting partners list"""
    print("\n🧪 Testing partners list...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get("http://localhost:8000/api/partners/", headers=headers)
        
        print(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            partners = response.json()
            print(f"✅ Partners retrieved successfully!")
            print(f"   Count: {len(partners)}")
            if partners:
                print(f"   Sample: {partners[0].get('name')}")
            return True
        else:
            print(f"❌ Failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_get_transactions(token):
    """Test getting transactions list"""
    print("\n🧪 Testing transactions list...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get("http://localhost:8000/api/transactions/", headers=headers)
        
        print(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            transactions = response.json()
            print(f"✅ Transactions retrieved successfully!")
            print(f"   Count: {len(transactions)}")
            if transactions:
                print(f"   Sample: {transactions[0].get('amount')} {transactions[0].get('currency')}")
            return True
        else:
            print(f"❌ Failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Creation Endpoints")
    print("=" * 50)
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication!")
        return
    
    # Test creating a partner
    partner_id = test_create_partner(token)
    
    if partner_id:
        # Test creating a transaction
        transaction_id = test_create_transaction(token, partner_id)
    
    # Test getting data
    partners_ok = test_get_partners(token)
    transactions_ok = test_get_transactions(token)
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    
    if partner_id and transaction_id and partners_ok and transactions_ok:
        print("🎉 All creation and retrieval endpoints are working!")
        print("💡 The UI should be able to:")
        print("   ✅ Create new partners")
        print("   ✅ Create new transactions") 
        print("   ✅ View partners list")
        print("   ✅ View transactions list")
        print("\n🌟 Try refreshing the dashboard - it should now show data!")
    else:
        print("⚠️  Some endpoints are not working properly.")
        print("🔧 Check the server logs for detailed error messages.")

if __name__ == "__main__":
    main()
